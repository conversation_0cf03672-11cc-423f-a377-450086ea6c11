<template>
	<div
		class="playback-controls-container"
		v-if="characterManager"
	>
		<div class="playback-controls-card">
			<div class="video-player-controls">
				<!-- 播放按钮居中 -->
				<div class="play-button-container">
					<div
						class="play-pause-btn"
						@click="togglePlayPause"
					>
						<el-icon
							v-if="isPlaying"
							class="pause-icon"
							><VideoPause
						/></el-icon>
						<el-icon
							v-else
							class="play-icon"
							><CaretRight
						/></el-icon>
					</div>
				</div>

				<!-- 右侧控制 -->
				<div class="controls-right">
					<div
						class="reset-btn"
						@click="handleReset"
						title="重置到开始"
					>
						<el-icon class="reset-icon"><RefreshLeft /></el-icon>
					</div>
					<div
						class="playback-status"
						:class="{ active: isPlaying }"
					>
						<span>{{ isPlaying ? '播放中' : '已暂停' }}</span>
					</div>
				</div>
			</div>

			<!-- 时间和进度条 -->
			<div class="time-progress-container">
				<div class="time-display current-time">
					{{ currentTimeDisplay }}
				</div>

				<div class="progress-bar-container">
					<div class="progress-bar-bg"></div>
					<div
						class="progress-bar-fill"
						:style="{ width: `${(currentFrame / maxFrame) * 100}%` }"
					></div>
					<div
						class="progress-handle"
						:style="{ left: `${(currentFrame / maxFrame) * 100}%` }"
					></div>
					<el-slider
						v-model="currentFrame"
						:min="0"
						:max="maxFrame"
						:show-tooltip="false"
						@change="handleFrameChange"
						@input="handleFrameInput"
						class="progress-slider"
					/>
				</div>

				<div class="time-display total-time">-{{ totalTime }}</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, onMounted, onUnmounted } from 'vue';
	import { VideoPause, CaretRight, RefreshLeft } from '@element-plus/icons-vue';

	const props = defineProps({
		characterManager: {
			type: Object,
			default: null,
		},
	});

	// 播放控制相关状态
	const currentFrame = ref(0);
	const maxFrame = ref(1000);
	const isPlaying = ref(false);
	const currentTimeDisplay = ref('0:00');
	const totalTime = ref('0:00');
	const isDragging = ref(false);

	// 播放/暂停切换
	const togglePlayPause = () => {
		if (isPlaying.value) {
			handlePause();
		} else {
			handlePlay();
		}
	};

	// 播放控制方法
	const handlePlay = () => {
		if (props.characterManager) {
			props.characterManager.play();
		}
	};

	const handlePause = () => {
		if (props.characterManager) {
			props.characterManager.pause();
		}
	};

	const handleReset = () => {
		if (props.characterManager) {
			props.characterManager.reset();
		}
	};

	const handleFrameChange = (value) => {
		isDragging.value = false;
		if (props.characterManager) {
			const frameIndex = Math.floor(value);
			props.characterManager.seekToFrame(frameIndex);
		}
	};

	const handleFrameInput = (value) => {
		isDragging.value = true;
	};

	// 更新播放状态
	const updatePlaybackStatus = () => {
		if (!props.characterManager) return;

		try {
			isPlaying.value = props.characterManager.isPlaying || false;

			if (!isDragging.value) {
				currentFrame.value = props.characterManager.timeIndex || 0;
			}

			// 获取时间信息
			if (props.characterManager.getTimeInfo) {
				const timeInfo = props.characterManager.getTimeInfo();
				currentTimeDisplay.value = timeInfo.currentTime;
				totalTime.value = timeInfo.totalTime;
			}

			// 更新最大帧数
			if (props.characterManager.moveData) {
				maxFrame.value = Math.max(
					1,
					props.characterManager.moveData.length - 1
				);
			}
		} catch (error) {
			console.error('更新播放状态时出错:', error);
		}
	};

	// 定时更新播放状态
	let updateInterval = null;

	onMounted(() => {
		// 每100ms更新一次播放状态
		updateInterval = setInterval(updatePlaybackStatus, 100);
	});

	onUnmounted(() => {
		if (updateInterval) {
			clearInterval(updateInterval);
		}
	});
</script>

<style scoped>
	/* 播放控制容器 */
	.playback-controls-container {
		position: absolute;
		bottom: 30px;
		left: 50%;
		transform: translateX(-50%);
		z-index: 1000;
		width: auto;
		animation: fadeIn 0.3s ease-out;
	}

	.playback-controls-card {
		background: #ffffff;
		border-radius: 8px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		overflow: hidden;
		border: 1px solid #e6e6e6;
		padding: 8px 12px;
		width: 60vw;
		max-width: 600px;
		min-width: 320px;
	}

	/* 视频播放器控制面板 */
	.video-player-controls {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-bottom: 8px;
		color: #333;
	}

	/* 播放按钮居中 */
	.play-button-container {
		display: flex;
		justify-content: center;
		width: 100%;
		position: relative;
	}

	.play-pause-btn {
		width: 36px;
		height: 36px;
		border-radius: 50%;
		background: #6698cb;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.play-pause-btn:hover {
		background: #5589bd;
	}

	.play-icon,
	.pause-icon {
		font-size: 18px;
		color: #ffffff;
	}

	/* 右侧控制 */
	.controls-right {
		position: absolute;
		right: 0;
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.reset-btn {
		width: 28px;
		height: 28px;
		border-radius: 50%;
		background: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.2s ease;
		border: 1px solid #e0e0e0;
	}

	.reset-btn:hover {
		background: #e8e8e8;
		border-color: #d0d0d0;
		transform: scale(1.05);
	}

	.reset-btn:active {
		transform: scale(0.95);
	}

	.reset-icon {
		font-size: 14px;
		color: #666;
	}

	.playback-status {
		font-size: 13px;
		color: #666;
		padding: 4px 10px;
		border-radius: 18px;
	}

	/* 时间和进度条容器 */
	.time-progress-container {
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.time-display {
		font-family: var(--app-font-mono);
		font-size: 13px;
		color: #666;
		white-space: nowrap;
	}

	.current-time {
		min-width: 45px;
	}

	.total-time {
		min-width: 45px;
		text-align: right;
	}

	/* 进度条 */
	.progress-bar-container {
		position: relative;
		height: 16px;
		flex: 1;
		cursor: pointer;
	}

	.progress-bar-bg {
		position: absolute;
		top: 7px;
		left: 0;
		right: 0;
		height: 4px;
		background: #e6e6e6;
		border-radius: 4px;
	}

	.progress-bar-fill {
		position: absolute;
		top: 7px;
		left: 0;
		height: 4px;
		background: #6698cb;
		border-radius: 4px;
		transition: width 0.1s linear;
	}

	.progress-handle {
		position: absolute;
		top: 3px;
		width: 12px;
		height: 12px;
		background: #6698cb;
		border-radius: 50%;
		transform: translateX(-50%);
		opacity: 0;
		transition: opacity 0.2s ease, transform 0.2s ease;
		border: 2px solid #fff;
		box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
	}

	.progress-bar-container:hover .progress-bar-bg {
		height: 6px;
		top: 6px;
	}

	.progress-bar-container:hover .progress-bar-fill {
		height: 6px;
		top: 6px;
	}

	.progress-bar-container:hover .progress-handle {
		opacity: 1;
		transform: translateX(-50%) scale(1.1);
	}

	.progress-slider {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		opacity: 0;
		cursor: pointer;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translate(-50%, 20px);
		}
		to {
			opacity: 1;
			transform: translate(-50%, 0);
		}
	}

	/* Element Plus 组件样式重写 */
	:deep(.el-slider__runway) {
		margin: 0;
		background: transparent;
		height: 16px;
		border-radius: 0;
	}

	:deep(.el-slider__bar) {
		display: none;
	}

	:deep(.el-slider__button-wrapper) {
		top: 7px;
		width: 0;
		height: 0;
	}

	:deep(.el-slider__button) {
		display: none;
	}

	/* 响应式设计 */
	@media (max-width: 768px) {
		.playback-controls-container {
			width: 80%;
			min-width: 280px;
			bottom: 20px;
		}

		.time-progress-container {
			gap: 6px;
		}
	}

	@media (max-width: 480px) {
		.playback-controls-card {
			padding: 6px 10px;
		}

		.play-pause-btn {
			width: 32px;
			height: 32px;
		}

		.reset-btn {
			width: 24px;
			height: 24px;
		}

		.reset-icon {
			font-size: 12px;
		}

		.playback-status {
			display: none;
		}
	}
</style>

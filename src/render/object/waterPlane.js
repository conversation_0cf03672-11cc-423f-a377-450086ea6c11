import * as THREE from 'three';
import { Water } from 'three/examples/jsm/objects/Water.js';
import { Sky } from 'three/examples/jsm/objects/Sky.js';

export class WaterPlane {
	constructor(width, height) {
		this.width = width;
		this.height = height;
		this.water = null;
		this.sky = null;
		this.sun = new THREE.Vector3();
		this.pmremGenerator = null;
		this.renderTarget = null;
	}

	createWaterPlane() {
		// 创建水面几何体
		const waterGeometry = new THREE.PlaneGeometry(10000, 10000);

		// 创建专业的Water对象
		this.water = new Water(waterGeometry, {
			textureWidth: 512,
			textureHeight: 512,
			waterNormals: new THREE.TextureLoader().load(
				'/waternormals.jpg',
				function (texture) {
					texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
				}
			),
			sunDirection: new THREE.Vector3(),
			sunColor: 0x4a90e2, // 柔和的蓝色太阳光
			waterColor: 0x0f2a3a, // 深海蓝色
			distortionScale: 3.7,
			fog: false,
		});

		// 设置水面位置和旋转
		this.water.rotation.x = -Math.PI / 2;
		this.water.position.y = -7.5;

		return this.water;
	}

	// 创建天空盒（可选）
	createSky(renderer, scene) {
		// 创建天空
		this.sky = new Sky();
		this.sky.scale.setScalar(10000);

		const skyUniforms = this.sky.material.uniforms;
		skyUniforms['turbidity'].value = 10;
		skyUniforms['rayleigh'].value = 2;
		skyUniforms['mieCoefficient'].value = 0.005;
		skyUniforms['mieDirectionalG'].value = 0.8;

		// 设置太阳位置
		const parameters = {
			elevation: 2,
			azimuth: 180,
		};

		this.updateSun(parameters);

		// 创建PMREM生成器用于环境贴图
		this.pmremGenerator = new THREE.PMREMGenerator(renderer);
		this.pmremGenerator.compileEquirectangularShader();

		return this.sky;
	}

	// 更新太阳位置
	updateSun(parameters) {
		const phi = THREE.MathUtils.degToRad(90 - parameters.elevation);
		const theta = THREE.MathUtils.degToRad(parameters.azimuth);

		this.sun.setFromSphericalCoords(1, phi, theta);

		if (this.sky) {
			this.sky.material.uniforms['sunPosition'].value.copy(this.sun);
		}

		if (this.water) {
			this.water.material.uniforms['sunDirection'].value
				.copy(this.sun)
				.normalize();
		}
	}

	// 更新水面动画（需要在渲染循环中调用）
	update(time) {
		if (this.water && this.water.material) {
			// 更新Water对象的时间uniform
			this.water.material.uniforms['time'].value += 1.0 / 60.0;
		}
	}

	// 设置水面颜色
	setWaterColor(color) {
		if (this.water && this.water.material) {
			this.water.material.uniforms['waterColor'].value.setHex(color);
		}
	}

	// 设置太阳光颜色
	setSunColor(color) {
		if (this.water && this.water.material) {
			this.water.material.uniforms['sunColor'].value.setHex(color);
		}
	}

	// 设置扭曲强度
	setDistortionScale(scale) {
		if (this.water && this.water.material) {
			this.water.material.uniforms['distortionScale'].value = scale;
		}
	}

	// 设置水面透明度（对于Water对象，需要通过其他方式实现）
	setWaterOpacity(opacity) {
		if (this.water && this.water.material) {
			// Water对象的透明度控制比较复杂，这里提供一个简化的实现
			this.water.material.transparent = opacity < 1.0;
			this.water.material.opacity = opacity;
		}
	}
}

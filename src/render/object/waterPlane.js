import * as THREE from 'three';
import { Water } from 'three/examples/jsm/objects/Water.js';
import { Sky } from 'three/examples/jsm/objects/Sky.js';

export class WaterPlane {
	constructor(width, height) {
		this.width = width;
		this.height = height;
		this.water = null;
		this.sky = null;
		this.sun = new THREE.Vector3();
	}

	createWaterPlane() {
		const waterGeometry = new THREE.PlaneGeometry(10000, 10000);

		// 预加载水面纹理
		const waterTexture = new THREE.TextureLoader().load('/waternormals.jpg');
		waterTexture.wrapS = waterTexture.wrapT = THREE.RepeatWrapping;
		waterTexture.repeat.set(8, 8);
		// 调整纹理的亮度和对比度
		waterTexture.encoding = THREE.sRGBEncoding;

		// 使用MeshBasicMaterial代替MeshPhongMaterial，确保颜色正确显示
		const waterMaterial = new THREE.MeshBasicMaterial({
			color: 0x66ccff, // 浅蓝色
			map: waterTexture,
			side: THREE.DoubleSide,
			transparent: true,
			opacity: 0.9, // 稍微透明一点
		});

		// 创建水面网格
		this.water = new THREE.Mesh(waterGeometry, waterMaterial);
		this.water.rotation.x = -Math.PI / 2;
		this.water.position.y = -7.5; // 稍微提高水面位置，使其更明显

		// 确保不接收阴影
		this.water.receiveShadow = false;

		// 设置动画更新所需的时间属性
		this.water.userData.time = 0;

		return this.water;
	}

	// 更新水面动画（需要在渲染循环中调用）
	update(time) {
		if (this.water && this.water.material) {
			// 更新纹理偏移以产生波纹动画效果
			if (this.water.material.map) {
				this.water.material.map.offset.x = time * 0.02; // 减慢动画速度
				this.water.material.map.offset.y = time * 0.01; // 减慢动画速度
				this.water.material.map.needsUpdate = true;
			}

			// 保存时间值
			this.water.userData.time = time;
		}
	}
}

// 模型配置数组
export const modelList = [
	// 老年男性模型
	{
		type: 'elder<PERSON>ale',
		label: '老年男',
		name: 'elder<PERSON>ale01',
		path: 'elderMale01.glb',
		texture: [
			{
				name: 'elderMale01',
				path: 'elderMale01.png',
			},
		],
	},
	// {
	// 	type: 'elder<PERSON>ale',
	// 	label: '老年男',
	// 	name: 'elderMale02',
	// 	path: 'elderMale02.glb',
	// 	texture: [
	// 		{
	// 			name: 'elderMale02',
	// 			path: 'elderMale02.png',
	// 		},
	// 	],
	// },
	{
		type: 'elderMale',
		label: '老年男',
		name: 'elderMale03',
		path: 'elderMale03.glb',
		texture: [
			{
				name: 'elderMale03',
				path: 'elderMale03.png',
			},
		],
	},
	// 老年女性模型
	{
		type: 'elderFemale',
		label: '老年女',
		name: 'elder<PERSON><PERSON><PERSON><PERSON>',
		path: 'elderFemale01.glb',
		texture: [
			{
				name: 'elder<PERSON>emale01',
				path: 'elderFemale01.png',
			},
		],
	},
	// {
	// 	type: 'elderFemale',
	// 	label: '老年女',
	// 	name: 'elderFemale02',
	// 	path: 'elderFemale02.glb',
	// 	texture: [
	// 		{
	// 			name: 'elderFemale02',
	// 			path: 'elderFemale02.png',
	// 		},
	// 	],
	// },
	{
		type: 'elderFemale',
		label: '老年女',
		name: 'elderFemale03',
		path: 'elderFemale03.glb',
		texture: [
			{
				name: 'elderFemale03',
				path: 'elderFemale03.png',
			},
		],
	},
	// 成年男性模型
	{
		type: 'adultMale',
		label: '成年男',
		name: 'adultMale01',
		path: 'adultMale01.glb',
		texture: [
			{
				name: 'adultMale01',
				path: 'adultMale01.png',
			},
		],
	},
	{
		type: 'adultMale',
		label: '成年男',
		name: 'adultMale02',
		path: 'adultMale02.glb',
		texture: [
			{
				name: 'adultMale02',
				path: 'adultMale02.png',
			},
		],
	},
	{
		type: 'adultMale',
		label: '成年男',
		name: 'adultMale03',
		path: 'adultMale03.glb',
		texture: [
			{
				name: 'adultMale03',
				path: 'adultMale03.png',
			},
		],
	},
	// 成年女性模型
	{
		type: 'adultFemale',
		label: '成年女',
		name: 'adultFemale01',
		path: 'adultFemale01.glb',
		texture: [
			{
				name: 'adultFemale01',
				path: 'adultFemale01.png',
			},
		],
	},
	{
		type: 'adultFemale',
		label: '成年女',
		name: 'adultFemale02',
		path: 'adultFemale02.glb',
		texture: [
			{
				name: 'adultFemale02',
				path: 'adultFemale02.png',
			},
		],
	},
	{
		type: 'adultFemale',
		label: '成年女',
		name: 'adultFemale03',
		path: 'adultFemale03.glb',
		texture: [
			{
				name: 'adultFemale03',
				path: 'adultFemale03.png',
			},
		],
	},
	// 儿童男性模型
	{
		type: 'childMale',
		label: '儿童男',
		name: 'childMale01',
		path: 'childMale01.glb',
		texture: [
			{
				name: 'childMale01',
				path: 'childMale01.png',
			},
		],
	},
	{
		type: 'childMale',
		label: '儿童男',
		name: 'childMale02',
		path: 'childMale02.glb',
		texture: [
			{
				name: 'childMale02',
				path: 'childMale02.png',
			},
		],
	},
	{
		type: 'childMale',
		label: '儿童男',
		name: 'childMale03',
		path: 'childMale03.glb',
		texture: [
			{
				name: 'childMale03',
				path: 'childMale03.png',
			},
		],
	},
	// 儿童女性模型
	// {
	// 	type: 'childFemale',
	// 	label: '儿童女',
	// 	name: 'childFemale01',
	// 	path: 'childFemale01.glb',
	// 	texture: [
	// 		{
	// 			name: 'childFemale01',
	// 			path: 'childFemale01.png',
	// 		},
	// 	],
	// },
	// {
	// 	type: 'childFemale',
	// 	label: '儿童女',
	// 	name: 'childFemale02',
	// 	path: 'childFemale02.glb',
	// 	texture: [
	// 		{
	// 			name: 'childFemale02',
	// 			path: 'childFemale02.png',
	// 		},
	// 	],
	// },
	// {
	// 	type: 'childFemale',
	// 	label: '儿童女',
	// 	name: 'childFemale03',
	// 	path: 'childFemale03.glb',
	// 	texture: [
	// 		{
	// 			name: 'childFemale03',
	// 			path: 'childFemale03.png',
	// 		},
	// 	],
	// },
];

// 工具函数：根据类型随机获取一个模型
export const getRandomModelByType = (type) => {
	const filteredModels = modelList.filter((model) => model.type === type);
	if (!filteredModels.length) return null;

	const randomIndex = Math.floor(Math.random() * filteredModels.length);
	return {
		model: filteredModels[randomIndex].name,
		texture: filteredModels[randomIndex].texture[0].name,
	};
};
export const modelTypes = [
	'adultMale',
	'adultFemale',
	'childMale',
	'childFemale',
	'elderMale',
	'elderFemale',
];

// 工具函数：根据类型随机获取一个贴图
export const getRandomTextureByType = (type) => {
	const filteredModels = modelList.filter((model) => model.type === type);
	if (!filteredModels.length) return null;

	const randomIndex = Math.floor(Math.random() * filteredModels.length);
	return {
		name: filteredModels[randomIndex].name,
		path: filteredModels[randomIndex].texturePath,
	};
};

export default modelList;
